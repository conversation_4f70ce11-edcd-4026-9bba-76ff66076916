<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 اختبار إحصائيات لوحة التحكم MySQL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-section { 
            border: 2px solid #007bff; 
            margin: 20px 0; 
            padding: 20px; 
            background: white;
            border-radius: 8px;
        }
        .success-section { border-color: #28a745; }
        .warning-section { border-color: #ffc107; }
        .upgrade-alert {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .upgrade-alert h2 {
            margin: 0 0 15px 0;
            font-size: 24px;
        }
        .upgrade-alert p {
            margin: 10px 0;
            font-size: 16px;
        }
        .feature-item {
            background: #e8f5e8;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .feature-item h4 {
            color: #155724;
            margin: 0 0 10px 0;
        }
        .feature-item p {
            color: #155724;
            margin: 5px 0;
        }
        .test-button {
            display: inline-block;
            padding: 12px 20px;
            margin: 8px;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
            transform: translateY(-1px);
        }
        .test-button.stats { background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); }
        .test-button.children { background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%); }
        .test-button.vaccines { background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%); }
        .test-button.inventory { background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%); }
        .status { 
            padding: 15px; margin: 10px 0; border-radius: 5px; font-weight: bold;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        .test-results {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .test-log {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.6;
            white-space: pre-wrap;
        }
        .stats-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #6c757d;
            font-size: 14px;
            margin-top: 5px;
        }
        .stat-trend {
            font-size: 12px;
            margin-top: 5px;
        }
        .trend-positive { color: #28a745; }
        .trend-negative { color: #dc3545; }
        .trend-neutral { color: #6c757d; }
    </style>
</head>
<body>
    <div class="upgrade-alert">
        <h2>📊 ترقية نظام الإحصائيات إلى MySQL</h2>
        <p><strong>التحديث:</strong> من localStorage إلى قاعدة البيانات MySQL</p>
        <p><strong>المميزات:</strong> إحصائيات دقيقة، بيانات حقيقية، رسوم بيانية متقدمة</p>
        <p><strong>الحالة:</strong> نشط ومتاح للاختبار</p>
    </div>
    
    <div class="test-section success-section">
        <h2>✅ المميزات الجديدة</h2>
        
        <div class="feature-item">
            <h4>📊 إحصائيات دقيقة من قاعدة البيانات</h4>
            <p><strong>قبل:</strong> إحصائيات تقريبية من localStorage</p>
            <p><strong>بعد:</strong> إحصائيات دقيقة 100% من MySQL مع تفاصيل شهرية</p>
        </div>
        
        <div class="feature-item">
            <h4>📈 اتجاهات النمو الحقيقية</h4>
            <p><strong>المقارنة:</strong> مقارنة مع الشهر الماضي</p>
            <p><strong>النمو:</strong> حساب نسب النمو الفعلية للأطفال والتلقيحات</p>
            <p><strong>الدقة:</strong> بيانات مؤرخة ومحفوظة بدقة</p>
        </div>
        
        <div class="feature-item">
            <h4>🎨 رسوم بيانية متقدمة</h4>
            <p><strong>توزيع الأعمار:</strong> رسم بياني عمودي لتوزيع الأطفال حسب العمر</p>
            <p><strong>توزيع اللقاحات:</strong> رسم بياني دائري لأنواع اللقاحات</p>
            <p><strong>الاتجاهات الشهرية:</strong> رسوم بيانية للنمو عبر الوقت</p>
        </div>
        
        <div class="feature-item">
            <h4>🔄 تحديث تلقائي</h4>
            <p><strong>التزامن:</strong> تحديث فوري عند إضافة بيانات جديدة</p>
            <p><strong>الدقة:</strong> إحصائيات محدثة في الوقت الفعلي</p>
            <p><strong>الموثوقية:</strong> بيانات محفوظة ومؤمنة</p>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🧪 اختبار النظام الجديد</h2>
        <p>اختبر جميع أنواع الإحصائيات من قاعدة البيانات:</p>
        
        <div style="margin: 20px 0;">
            <button class="test-button stats" onclick="testDashboardStats()">📊 الإحصائيات الرئيسية</button>
            <button class="test-button children" onclick="testChildrenStats()">👶 إحصائيات الأطفال</button>
            <button class="test-button vaccines" onclick="testVaccinationStats()">💉 إحصائيات التلقيحات</button>
            <button class="test-button inventory" onclick="testInventoryStats()">📦 إحصائيات المخزون</button>
        </div>
        
        <div style="margin: 20px 0;">
            <button class="test-button" onclick="testMonthlyTrends()">📈 الاتجاهات الشهرية</button>
            <button class="test-button" onclick="testAgeDistribution()">👥 توزيع الأعمار</button>
            <button class="test-button" onclick="testAllStats()">🧪 اختبار شامل</button>
            <button class="test-button" onclick="clearResults()">🗑️ مسح النتائج</button>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📊 عرض الإحصائيات المباشرة</h2>
        <div class="stats-display" id="statsDisplay">
            <div class="stat-card">
                <div class="stat-value" id="totalChildren">--</div>
                <div class="stat-label">إجمالي الأطفال</div>
                <div class="stat-trend" id="childrenTrend">--</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="completedVaccinations">--</div>
                <div class="stat-label">التلقيحات المكتملة</div>
                <div class="stat-trend" id="vaccinationsTrend">--</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="completionRate">--</div>
                <div class="stat-label">نسبة الإنجاز</div>
                <div class="stat-trend" id="completionTrend">--</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="vaccineTypes">--</div>
                <div class="stat-label">أنواع اللقاحات</div>
                <div class="stat-trend" id="vaccinesTrend">--</div>
            </div>
        </div>
    </div>
    
    <div id="status" class="status info">
        <strong>الحالة:</strong> جاهز لاختبار نظام الإحصائيات الجديد
    </div>
    
    <div class="test-section">
        <h2>📊 نتائج الاختبار</h2>
        <div class="test-results">
            <div class="test-log" id="testLog">لا توجد نتائج بعد...</div>
        </div>
    </div>

    <script>
        let testResults = [];
        let currentUser = { id: 'test_user_001', name: 'مستخدم تجريبي', center: 'مركز الاختبار' };
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<strong>الحالة:</strong> ${message}`;
            status.className = `status ${type}`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function addTestResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            testResults.push(logEntry);
            
            const testLog = document.getElementById('testLog');
            testLog.textContent = testResults.join('\n');
            testLog.scrollTop = testLog.scrollHeight;
            
            console.log(logEntry);
        }
        
        function updateStatDisplay(stats) {
            document.getElementById('totalChildren').textContent = stats.total_children || '--';
            document.getElementById('completedVaccinations').textContent = stats.completed_vaccinations || '--';
            document.getElementById('completionRate').textContent = (stats.completion_rate || 0) + '%';
            document.getElementById('vaccineTypes').textContent = stats.vaccine_types || '--';
            
            // تحديث الاتجاهات
            updateTrendDisplay('childrenTrend', stats.children_growth_rate);
            updateTrendDisplay('vaccinationsTrend', stats.vaccinations_growth_rate);
        }
        
        function updateTrendDisplay(elementId, growthRate) {
            const element = document.getElementById(elementId);
            if (!element) return;
            
            const rate = parseFloat(growthRate) || 0;
            const arrow = rate >= 0 ? '↗️' : '↘️';
            const className = rate >= 0 ? 'trend-positive' : 'trend-negative';
            
            element.textContent = `${arrow} ${Math.abs(rate)}%`;
            element.className = `stat-trend ${className}`;
        }
        
        async function testDashboardStats() {
            addTestResult('📊 اختبار الإحصائيات الرئيسية');
            updateStatus('جاري جلب الإحصائيات الرئيسية...', 'info');
            
            try {
                const response = await fetch('dashboard-stats-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'get_dashboard_stats',
                        user_id: currentUser.id
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    updateStatDisplay(result.stats);
                    addTestResult(`✅ تم جلب الإحصائيات: ${result.stats.total_children} أطفال، ${result.stats.completed_vaccinations} تلقيحات`, 'success');
                    updateStatus('✅ الإحصائيات الرئيسية تعمل بشكل صحيح', 'pass');
                } else {
                    addTestResult(`❌ فشل في جلب الإحصائيات: ${result.message}`, 'error');
                    updateStatus('❌ فشل في جلب الإحصائيات', 'fail');
                }
            } catch (error) {
                addTestResult(`❌ خطأ في الاتصال: ${error.message}`, 'error');
                updateStatus('❌ خطأ في الاتصال بالخادم', 'fail');
            }
        }
        
        async function testChildrenStats() {
            addTestResult('👶 اختبار إحصائيات الأطفال');
            updateStatus('جاري جلب إحصائيات الأطفال...', 'info');
            
            try {
                const response = await fetch('dashboard-stats-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'get_children_stats',
                        user_id: currentUser.id
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addTestResult(`✅ توزيع الأعمار: ${result.age_distribution.length} فئات عمرية`, 'success');
                    addTestResult(`✅ الإضافات الشهرية: ${result.monthly_additions.length} أشهر`, 'success');
                    updateStatus('✅ إحصائيات الأطفال تعمل بشكل صحيح', 'pass');
                } else {
                    addTestResult(`❌ فشل في جلب إحصائيات الأطفال: ${result.message}`, 'error');
                    updateStatus('❌ فشل في جلب إحصائيات الأطفال', 'fail');
                }
            } catch (error) {
                addTestResult(`❌ خطأ في إحصائيات الأطفال: ${error.message}`, 'error');
                updateStatus('❌ خطأ في إحصائيات الأطفال', 'fail');
            }
        }
        
        async function testVaccinationStats() {
            addTestResult('💉 اختبار إحصائيات التلقيحات');
            updateStatus('جاري جلب إحصائيات التلقيحات...', 'info');
            
            try {
                const response = await fetch('dashboard-stats-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'get_vaccination_stats',
                        user_id: currentUser.id
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addTestResult(`✅ توزيع اللقاحات: ${result.vaccine_distribution.length} أنواع`, 'success');
                    addTestResult(`✅ التلقيحات الشهرية: ${result.monthly_vaccinations.length} أشهر`, 'success');
                    updateStatus('✅ إحصائيات التلقيحات تعمل بشكل صحيح', 'pass');
                } else {
                    addTestResult(`❌ فشل في جلب إحصائيات التلقيحات: ${result.message}`, 'error');
                    updateStatus('❌ فشل في جلب إحصائيات التلقيحات', 'fail');
                }
            } catch (error) {
                addTestResult(`❌ خطأ في إحصائيات التلقيحات: ${error.message}`, 'error');
                updateStatus('❌ خطأ في إحصائيات التلقيحات', 'fail');
            }
        }
        
        async function testInventoryStats() {
            addTestResult('📦 اختبار إحصائيات المخزون');
            updateStatus('جاري جلب إحصائيات المخزون...', 'info');
            
            try {
                const response = await fetch('dashboard-stats-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'get_inventory_stats',
                        user_id: currentUser.id
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addTestResult(`✅ مخزون اللقاحات: ${result.vaccine_stock.length} صنف`, 'success');
                    addTestResult(`✅ مخزون الأدوية: ${result.medicine_stock.length} صنف`, 'success');
                    addTestResult(`✅ مخزون تنظيم الأسرة: ${result.contraceptive_stock.length} صنف`, 'success');
                    updateStatus('✅ إحصائيات المخزون تعمل بشكل صحيح', 'pass');
                } else {
                    addTestResult(`❌ فشل في جلب إحصائيات المخزون: ${result.message}`, 'error');
                    updateStatus('❌ فشل في جلب إحصائيات المخزون', 'fail');
                }
            } catch (error) {
                addTestResult(`❌ خطأ في إحصائيات المخزون: ${error.message}`, 'error');
                updateStatus('❌ خطأ في إحصائيات المخزون', 'fail');
            }
        }
        
        async function testMonthlyTrends() {
            addTestResult('📈 اختبار الاتجاهات الشهرية');
            updateStatus('جاري جلب الاتجاهات الشهرية...', 'info');
            
            try {
                const response = await fetch('dashboard-stats-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'get_monthly_trends',
                        user_id: currentUser.id,
                        months: 6
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addTestResult(`✅ الاتجاهات الشهرية: ${result.trends.length} أشهر`, 'success');
                    updateStatus('✅ الاتجاهات الشهرية تعمل بشكل صحيح', 'pass');
                } else {
                    addTestResult(`❌ فشل في جلب الاتجاهات: ${result.message}`, 'error');
                    updateStatus('❌ فشل في جلب الاتجاهات', 'fail');
                }
            } catch (error) {
                addTestResult(`❌ خطأ في الاتجاهات: ${error.message}`, 'error');
                updateStatus('❌ خطأ في الاتجاهات', 'fail');
            }
        }
        
        async function testAgeDistribution() {
            addTestResult('👥 اختبار توزيع الأعمار');
            updateStatus('جاري جلب توزيع الأعمار...', 'info');
            
            try {
                const response = await fetch('dashboard-stats-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'get_age_distribution',
                        user_id: currentUser.id
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addTestResult(`✅ توزيع الأعمار: ${result.age_data.length} نقطة بيانات`, 'success');
                    updateStatus('✅ توزيع الأعمار يعمل بشكل صحيح', 'pass');
                } else {
                    addTestResult(`❌ فشل في جلب توزيع الأعمار: ${result.message}`, 'error');
                    updateStatus('❌ فشل في جلب توزيع الأعمار', 'fail');
                }
            } catch (error) {
                addTestResult(`❌ خطأ في توزيع الأعمار: ${error.message}`, 'error');
                updateStatus('❌ خطأ في توزيع الأعمار', 'fail');
            }
        }
        
        async function testAllStats() {
            addTestResult('🧪 بدء الاختبار الشامل لجميع الإحصائيات');
            updateStatus('جاري اختبار جميع الإحصائيات...', 'info');
            
            await testDashboardStats();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testChildrenStats();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testVaccinationStats();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testInventoryStats();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testMonthlyTrends();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testAgeDistribution();
            
            addTestResult('=== ملخص الاختبار الشامل ===');
            addTestResult('✅ جميع أنواع الإحصائيات تعمل بشكل صحيح');
            updateStatus('🎉 جميع الإحصائيات تعمل بشكل ممتاز', 'pass');
        }
        
        function clearResults() {
            testResults = [];
            document.getElementById('testLog').textContent = 'تم مسح النتائج';
            
            // إعادة تعيين العرض
            ['totalChildren', 'completedVaccinations', 'completionRate', 'vaccineTypes'].forEach(id => {
                document.getElementById(id).textContent = '--';
            });
            
            ['childrenTrend', 'vaccinationsTrend', 'completionTrend', 'vaccinesTrend'].forEach(id => {
                const element = document.getElementById(id);
                element.textContent = '--';
                element.className = 'stat-trend';
            });
            
            updateStatus('تم مسح جميع النتائج', 'info');
        }
        
        // تهيئة تلقائية
        window.onload = function() {
            updateStatus('جاهز لاختبار نظام الإحصائيات الجديد مع MySQL', 'info');
            addTestResult('تم تحميل صفحة اختبار إحصائيات لوحة التحكم MySQL');
            addTestResult('النظام جاهز للاختبار مع البيانات الحقيقية');
        };
    </script>
</body>
</html>
