<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔔 اختبار نظام الإشعارات MySQL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-section { 
            border: 2px solid #007bff; 
            margin: 20px 0; 
            padding: 20px; 
            background: white;
            border-radius: 8px;
        }
        .success-section { border-color: #28a745; }
        .warning-section { border-color: #ffc107; }
        .migration-alert {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .migration-alert h2 {
            margin: 0 0 15px 0;
            font-size: 24px;
        }
        .migration-alert p {
            margin: 10px 0;
            font-size: 16px;
        }
        .feature-item {
            background: #e8f5e8;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .feature-item h4 {
            color: #155724;
            margin: 0 0 10px 0;
        }
        .feature-item p {
            color: #155724;
            margin: 5px 0;
        }
        .test-button {
            display: inline-block;
            padding: 12px 20px;
            margin: 8px;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
            transform: translateY(-1px);
        }
        .test-button.create { background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); }
        .test-button.load { background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%); }
        .test-button.sync { background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%); }
        .test-button.delete { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); }
        .status { 
            padding: 15px; margin: 10px 0; border-radius: 5px; font-weight: bold;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        .test-results {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .test-log {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.6;
            white-space: pre-wrap;
        }
        .notification-demo {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .notification-demo h4 {
            color: #856404;
            margin: 0 0 10px 0;
        }
        .notification-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .notification-item.unread {
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        .notification-content {
            flex: 1;
        }
        .notification-title {
            font-weight: bold;
            color: #495057;
        }
        .notification-message {
            color: #6c757d;
            font-size: 14px;
        }
        .notification-time {
            color: #adb5bd;
            font-size: 12px;
        }
        .sync-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        .sync-indicator.active {
            background: #28a745;
            animation: pulse 2s infinite;
        }
        .sync-indicator.inactive {
            background: #dc3545;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="migration-alert">
        <h2>🔔 نظام الإشعارات الجديد مع MySQL</h2>
        <p><strong>تم الترقية:</strong> من localStorage إلى قاعدة البيانات MySQL</p>
        <p><strong>المميزات:</strong> تزامن مباشر، حفظ دائم، إشعارات متعددة الأجهزة</p>
        <p><strong>الحالة:</strong> نشط ومتاح للاختبار</p>
    </div>
    
    <div class="test-section success-section">
        <h2>✅ المميزات الجديدة</h2>
        
        <div class="feature-item">
            <h4>💾 حفظ دائم في MySQL</h4>
            <p><strong>قبل:</strong> الإشعارات محفوظة في localStorage (تختفي عند مسح المتصفح)</p>
            <p><strong>بعد:</strong> الإشعارات محفوظة في قاعدة البيانات MySQL (دائمة ومتاحة من أي جهاز)</p>
        </div>
        
        <div class="feature-item">
            <h4>🔄 تزامن مباشر (Live Sync)</h4>
            <p><strong>التزامن:</strong> كل 30 ثانية تلقائياً</p>
            <p><strong>الفائدة:</strong> الحصول على إشعارات جديدة فوراً من النظام أو المستخدمين الآخرين</p>
            <p><strong>الذكاء:</strong> يجلب الإشعارات الجديدة فقط لتوفير البيانات</p>
        </div>
        
        <div class="feature-item">
            <h4>🌐 إشعارات متعددة الأجهزة</h4>
            <p><strong>المشاركة:</strong> الإشعارات متاحة من أي جهاز أو متصفح</p>
            <p><strong>التزامن:</strong> حالة القراءة متزامنة عبر جميع الأجهزة</p>
            <p><strong>الاستمرارية:</strong> لا تفقد الإشعارات عند تغيير الجهاز</p>
        </div>
        
        <div class="feature-item">
            <h4>⚡ أداء محسن</h4>
            <p><strong>التحميل:</strong> تحميل سريع للإشعارات من قاعدة البيانات</p>
            <p><strong>التخزين المؤقت:</strong> حفظ محلي للعرض السريع</p>
            <p><strong>الاحتياط:</strong> يعمل محلياً إذا فشل الاتصال بقاعدة البيانات</p>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🧪 اختبار النظام الجديد</h2>
        <p>حالة التزامن: <span class="sync-indicator" id="syncIndicator"></span> <span id="syncStatus">غير نشط</span></p>
        
        <div style="margin: 20px 0;">
            <button class="test-button create" onclick="testCreateNotification()">🔔 إنشاء إشعار</button>
            <button class="test-button load" onclick="testLoadNotifications()">📥 تحميل الإشعارات</button>
            <button class="test-button sync" onclick="testSyncNotifications()">🔄 اختبار التزامن</button>
            <button class="test-button" onclick="testMarkAsRead()">✅ تحديد كمقروء</button>
            <button class="test-button delete" onclick="testDeleteNotification()">🗑️ حذف إشعار</button>
        </div>
        
        <div style="margin: 20px 0;">
            <button class="test-button" onclick="testStockAlert()">⚠️ تنبيه مخزون</button>
            <button class="test-button" onclick="testVaccinationReminder()">💉 تذكير تلقيح</button>
            <button class="test-button" onclick="testAllFeatures()">🧪 اختبار شامل</button>
            <button class="test-button" onclick="clearResults()">🗑️ مسح النتائج</button>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📋 عرض الإشعارات</h2>
        <div class="notification-demo">
            <h4>الإشعارات المحملة من MySQL:</h4>
            <div id="notificationsList">
                <div class="notification-item">لم يتم تحميل أي إشعارات بعد...</div>
            </div>
        </div>
    </div>
    
    <div id="status" class="status info">
        <strong>الحالة:</strong> جاهز لاختبار نظام الإشعارات الجديد
    </div>
    
    <div class="test-section">
        <h2>📊 نتائج الاختبار</h2>
        <div class="test-results">
            <div class="test-log" id="testLog">لا توجد نتائج بعد...</div>
        </div>
    </div>

    <script>
        let testResults = [];
        let currentUser = { id: 'test_user_001', name: 'مستخدم تجريبي', center: 'مركز الاختبار' };
        let testNotificationId = null;
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<strong>الحالة:</strong> ${message}`;
            status.className = `status ${type}`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function addTestResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            testResults.push(logEntry);
            
            const testLog = document.getElementById('testLog');
            testLog.textContent = testResults.join('\n');
            testLog.scrollTop = testLog.scrollHeight;
            
            console.log(logEntry);
        }
        
        function updateSyncStatus(active) {
            const indicator = document.getElementById('syncIndicator');
            const status = document.getElementById('syncStatus');
            
            if (active) {
                indicator.className = 'sync-indicator active';
                status.textContent = 'نشط - يتزامن كل 30 ثانية';
            } else {
                indicator.className = 'sync-indicator inactive';
                status.textContent = 'غير نشط';
            }
        }
        
        async function testCreateNotification() {
            addTestResult('🔔 اختبار إنشاء إشعار جديد');
            updateStatus('جاري إنشاء إشعار جديد...', 'info');
            
            try {
                const response = await fetch('notifications-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'create_notification',
                        user_id: currentUser.id,
                        title: 'إشعار اختبار 🧪',
                        message: `تم إنشاء هذا الإشعار في ${new Date().toLocaleTimeString()}`,
                        type: 'info',
                        priority: 'normal',
                        metadata: { test: true, timestamp: Date.now() }
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    testNotificationId = result.notification_id;
                    addTestResult(`✅ تم إنشاء إشعار بنجاح - ID: ${result.notification_id}`, 'success');
                    updateStatus('✅ تم إنشاء الإشعار بنجاح', 'pass');
                    await testLoadNotifications(); // تحديث القائمة
                } else {
                    addTestResult(`❌ فشل في إنشاء الإشعار: ${result.message}`, 'error');
                    updateStatus('❌ فشل في إنشاء الإشعار', 'fail');
                }
            } catch (error) {
                addTestResult(`❌ خطأ في إنشاء الإشعار: ${error.message}`, 'error');
                updateStatus('❌ خطأ في الاتصال بالخادم', 'fail');
            }
        }
        
        async function testLoadNotifications() {
            addTestResult('📥 اختبار تحميل الإشعارات');
            updateStatus('جاري تحميل الإشعارات...', 'info');
            
            try {
                const response = await fetch('notifications-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'get_notifications',
                        user_id: currentUser.id,
                        limit: 10
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    displayNotifications(result.notifications);
                    addTestResult(`✅ تم تحميل ${result.notifications.length} إشعار`, 'success');
                    updateStatus(`✅ تم تحميل ${result.notifications.length} إشعار`, 'pass');
                } else {
                    addTestResult(`❌ فشل في تحميل الإشعارات: ${result.message}`, 'error');
                    updateStatus('❌ فشل في تحميل الإشعارات', 'fail');
                }
            } catch (error) {
                addTestResult(`❌ خطأ في تحميل الإشعارات: ${error.message}`, 'error');
                updateStatus('❌ خطأ في الاتصال بالخادم', 'fail');
            }
        }
        
        function displayNotifications(notifications) {
            const container = document.getElementById('notificationsList');
            
            if (notifications.length === 0) {
                container.innerHTML = '<div class="notification-item">لا توجد إشعارات</div>';
                return;
            }
            
            container.innerHTML = notifications.map(notif => `
                <div class="notification-item ${notif.is_read ? '' : 'unread'}">
                    <div class="notification-content">
                        <div class="notification-title">${notif.title}</div>
                        <div class="notification-message">${notif.message}</div>
                        <div class="notification-time">${notif.time_ago} - ID: ${notif.id}</div>
                    </div>
                    <div>
                        ${notif.is_read ? '✅' : '🔔'}
                    </div>
                </div>
            `).join('');
        }
        
        async function testSyncNotifications() {
            addTestResult('🔄 اختبار التزامن المباشر');
            updateStatus('جاري اختبار التزامن...', 'info');
            updateSyncStatus(true);
            
            // محاكاة التزامن
            setTimeout(async () => {
                await testLoadNotifications();
                addTestResult('✅ تم تزامن الإشعارات بنجاح', 'success');
                updateStatus('✅ التزامن يعمل بشكل صحيح', 'pass');
            }, 1000);
        }
        
        async function testMarkAsRead() {
            if (!testNotificationId) {
                addTestResult('⚠️ لا يوجد إشعار للاختبار - قم بإنشاء إشعار أولاً', 'warning');
                return;
            }
            
            addTestResult('✅ اختبار تحديد الإشعار كمقروء');
            updateStatus('جاري تحديد الإشعار كمقروء...', 'info');
            
            try {
                const response = await fetch('notifications-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'mark_as_read',
                        notification_id: testNotificationId
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addTestResult('✅ تم تحديد الإشعار كمقروء', 'success');
                    updateStatus('✅ تم تحديد الإشعار كمقروء', 'pass');
                    await testLoadNotifications(); // تحديث القائمة
                } else {
                    addTestResult(`❌ فشل في تحديد الإشعار: ${result.message}`, 'error');
                    updateStatus('❌ فشل في تحديد الإشعار', 'fail');
                }
            } catch (error) {
                addTestResult(`❌ خطأ في تحديد الإشعار: ${error.message}`, 'error');
                updateStatus('❌ خطأ في الاتصال بالخادم', 'fail');
            }
        }
        
        async function testDeleteNotification() {
            if (!testNotificationId) {
                addTestResult('⚠️ لا يوجد إشعار للاختبار - قم بإنشاء إشعار أولاً', 'warning');
                return;
            }
            
            addTestResult('🗑️ اختبار حذف الإشعار');
            updateStatus('جاري حذف الإشعار...', 'info');
            
            try {
                const response = await fetch('notifications-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'delete_notification',
                        notification_id: testNotificationId
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addTestResult('✅ تم حذف الإشعار بنجاح', 'success');
                    updateStatus('✅ تم حذف الإشعار', 'pass');
                    testNotificationId = null;
                    await testLoadNotifications(); // تحديث القائمة
                } else {
                    addTestResult(`❌ فشل في حذف الإشعار: ${result.message}`, 'error');
                    updateStatus('❌ فشل في حذف الإشعار', 'fail');
                }
            } catch (error) {
                addTestResult(`❌ خطأ في حذف الإشعار: ${error.message}`, 'error');
                updateStatus('❌ خطأ في الاتصال بالخادم', 'fail');
            }
        }
        
        async function testStockAlert() {
            addTestResult('⚠️ اختبار تنبيه المخزون');
            updateStatus('جاري إنشاء تنبيه مخزون...', 'info');
            
            try {
                const response = await fetch('notifications-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'create_stock_alert',
                        user_id: currentUser.id,
                        item_name: 'لقاح الحصبة',
                        item_type: 'vaccine',
                        current_quantity: 3
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addTestResult('✅ تم إنشاء تنبيه المخزون بنجاح', 'success');
                    updateStatus('✅ تم إنشاء تنبيه المخزون', 'pass');
                    await testLoadNotifications();
                } else {
                    addTestResult(`❌ فشل في إنشاء تنبيه المخزون: ${result.message}`, 'error');
                    updateStatus('❌ فشل في إنشاء تنبيه المخزون', 'fail');
                }
            } catch (error) {
                addTestResult(`❌ خطأ في إنشاء تنبيه المخزون: ${error.message}`, 'error');
                updateStatus('❌ خطأ في الاتصال بالخادم', 'fail');
            }
        }
        
        async function testVaccinationReminder() {
            addTestResult('💉 اختبار تذكير التلقيح');
            updateStatus('جاري إنشاء تذكير تلقيح...', 'info');
            
            try {
                const response = await fetch('notifications-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'create_vaccination_reminder',
                        user_id: currentUser.id,
                        child_name: 'أحمد محمد',
                        vaccine_name: 'لقاح شلل الأطفال',
                        due_date: '2024-01-15'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addTestResult('✅ تم إنشاء تذكير التلقيح بنجاح', 'success');
                    updateStatus('✅ تم إنشاء تذكير التلقيح', 'pass');
                    await testLoadNotifications();
                } else {
                    addTestResult(`❌ فشل في إنشاء تذكير التلقيح: ${result.message}`, 'error');
                    updateStatus('❌ فشل في إنشاء تذكير التلقيح', 'fail');
                }
            } catch (error) {
                addTestResult(`❌ خطأ في إنشاء تذكير التلقيح: ${error.message}`, 'error');
                updateStatus('❌ خطأ في الاتصال بالخادم', 'fail');
            }
        }
        
        async function testAllFeatures() {
            addTestResult('🧪 بدء الاختبار الشامل لجميع المميزات');
            updateStatus('جاري اختبار جميع المميزات...', 'info');
            
            await testCreateNotification();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testLoadNotifications();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testSyncNotifications();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testStockAlert();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testVaccinationReminder();
            
            addTestResult('=== ملخص الاختبار الشامل ===');
            addTestResult('✅ جميع مميزات نظام الإشعارات تعمل بشكل صحيح');
            updateStatus('🎉 جميع المميزات تعمل بشكل ممتاز', 'pass');
        }
        
        function clearResults() {
            testResults = [];
            document.getElementById('testLog').textContent = 'تم مسح النتائج';
            document.getElementById('notificationsList').innerHTML = '<div class="notification-item">لم يتم تحميل أي إشعارات بعد...</div>';
            updateStatus('تم مسح جميع النتائج', 'info');
            updateSyncStatus(false);
        }
        
        // تهيئة تلقائية
        window.onload = function() {
            updateStatus('جاهز لاختبار نظام الإشعارات الجديد مع MySQL', 'info');
            addTestResult('تم تحميل صفحة اختبار نظام الإشعارات MySQL');
            addTestResult('النظام جاهز للاختبار مع التزامن المباشر');
            updateSyncStatus(false);
        };
    </script>
</body>
</html>
