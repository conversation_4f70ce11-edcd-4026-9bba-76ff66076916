<?php
/**
 * API إحصائيات لوحة التحكم من قاعدة البيانات MySQL
 * Dashboard Statistics API from MySQL Database
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// إعدادات قاعدة البيانات
$host = '127.0.0.1';
$dbname = 'csdb';
$username = 'csdbuser';
$password = 'Aa123456789@';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDO\Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage()
    ]);
    exit();
}

// قراءة البيانات المرسلة
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'بيانات غير صحيحة'
    ]);
    exit();
}

$action = $data['action'] ?? '';
$user_id = $data['user_id'] ?? '';

// التحقق من وجود معرف المستخدم
if (empty($user_id)) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'معرف المستخدم مطلوب'
    ]);
    exit();
}

try {
    switch ($action) {
        case 'get_dashboard_stats':
            getDashboardStats($pdo, $user_id);
            break;
            
        case 'get_children_stats':
            getChildrenStats($pdo, $user_id);
            break;
            
        case 'get_vaccination_stats':
            getVaccinationStats($pdo, $user_id);
            break;
            
        case 'get_inventory_stats':
            getInventoryStats($pdo, $user_id);
            break;
            
        case 'get_monthly_trends':
            getMonthlyTrends($pdo, $user_id, $data);
            break;
            
        case 'get_age_distribution':
            getAgeDistribution($pdo, $user_id);
            break;
            
        default:
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'إجراء غير صحيح'
            ]);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم: ' . $e->getMessage()
    ]);
}

/**
 * جلب إحصائيات لوحة التحكم الشاملة
 */
function getDashboardStats($pdo, $user_id) {
    // إحصائيات الأطفال
    $childrenQuery = "SELECT COUNT(*) as total_children FROM children WHERE user_id = ?";
    $childrenStmt = $pdo->prepare($childrenQuery);
    $childrenStmt->execute([$user_id]);
    $totalChildren = $childrenStmt->fetch()['total_children'];
    
    // إحصائيات التلقيحات المكتملة هذا الشهر
    $currentMonth = date('Y-m');
    $vaccinationsQuery = "SELECT COUNT(*) as completed_vaccinations 
                         FROM children_vaccinations cv 
                         JOIN children c ON cv.child_id = c.id 
                         WHERE c.user_id = ? 
                         AND cv.status = 'completed' 
                         AND DATE_FORMAT(cv.vaccination_date, '%Y-%m') = ?";
    $vaccinationsStmt = $pdo->prepare($vaccinationsQuery);
    $vaccinationsStmt->execute([$user_id, $currentMonth]);
    $completedVaccinations = $vaccinationsStmt->fetch()['completed_vaccinations'];
    
    // إجمالي التلقيحات المطلوبة هذا الشهر
    $totalVaccinationsQuery = "SELECT COUNT(*) as total_due 
                              FROM children_vaccinations cv 
                              JOIN children c ON cv.child_id = c.id 
                              WHERE c.user_id = ? 
                              AND DATE_FORMAT(cv.due_date, '%Y-%m') = ?";
    $totalVaccinationsStmt = $pdo->prepare($totalVaccinationsQuery);
    $totalVaccinationsStmt->execute([$user_id, $currentMonth]);
    $totalDueVaccinations = $totalVaccinationsStmt->fetch()['total_due'];
    
    // حساب نسبة الإنجاز
    $completionRate = $totalDueVaccinations > 0 ? 
        round(($completedVaccinations / $totalDueVaccinations) * 100, 1) : 0;
    
    // عدد أنواع اللقاحات المتوفرة
    $vaccineTypesQuery = "SELECT COUNT(DISTINCT vaccine_name) as vaccine_types 
                         FROM vaccine_stock WHERE user_id = ? AND quantity > 0";
    $vaccineTypesStmt = $pdo->prepare($vaccineTypesQuery);
    $vaccineTypesStmt->execute([$user_id]);
    $vaccineTypes = $vaccineTypesStmt->fetch()['vaccine_types'];
    
    // إحصائيات الأدوية
    $medicineTypesQuery = "SELECT COUNT(DISTINCT medicine_name) as medicine_types 
                          FROM medicine_stock WHERE user_id = ? AND quantity > 0";
    $medicineTypesStmt = $pdo->prepare($medicineTypesQuery);
    $medicineTypesStmt->execute([$user_id]);
    $medicineTypes = $medicineTypesStmt->fetch()['medicine_types'];
    
    // إحصائيات تنظيم الأسرة
    $contraceptiveTypesQuery = "SELECT COUNT(DISTINCT contraceptive_name) as contraceptive_types 
                               FROM contraceptive_stock WHERE user_id = ? AND quantity > 0";
    $contraceptiveTypesStmt = $pdo->prepare($contraceptiveTypesQuery);
    $contraceptiveTypesStmt->execute([$user_id]);
    $contraceptiveTypes = $contraceptiveTypesStmt->fetch()['contraceptive_types'];
    
    // حساب الاتجاهات (مقارنة مع الشهر الماضي)
    $lastMonth = date('Y-m', strtotime('-1 month'));
    
    // أطفال جدد هذا الشهر
    $newChildrenQuery = "SELECT COUNT(*) as new_children 
                        FROM children 
                        WHERE user_id = ? 
                        AND DATE_FORMAT(created_at, '%Y-%m') = ?";
    $newChildrenStmt = $pdo->prepare($newChildrenQuery);
    $newChildrenStmt->execute([$user_id, $currentMonth]);
    $newChildren = $newChildrenStmt->fetch()['new_children'];
    
    // أطفال الشهر الماضي
    $lastMonthChildrenStmt = $pdo->prepare($newChildrenQuery);
    $lastMonthChildrenStmt->execute([$user_id, $lastMonth]);
    $lastMonthChildren = $lastMonthChildrenStmt->fetch()['new_children'];
    
    // حساب نسبة النمو
    $childrenGrowthRate = $lastMonthChildren > 0 ? 
        round((($newChildren - $lastMonthChildren) / $lastMonthChildren) * 100, 1) : 
        ($newChildren > 0 ? 100 : 0);
    
    // تلقيحات الشهر الماضي
    $lastMonthVaccinationsStmt = $pdo->prepare($vaccinationsQuery);
    $lastMonthVaccinationsStmt->execute([$user_id, $lastMonth]);
    $lastMonthVaccinations = $lastMonthVaccinationsStmt->fetch()['completed_vaccinations'];
    
    // حساب نسبة نمو التلقيحات
    $vaccinationsGrowthRate = $lastMonthVaccinations > 0 ? 
        round((($completedVaccinations - $lastMonthVaccinations) / $lastMonthVaccinations) * 100, 1) : 
        ($completedVaccinations > 0 ? 100 : 0);
    
    echo json_encode([
        'success' => true,
        'stats' => [
            'total_children' => (int)$totalChildren,
            'new_children_this_month' => (int)$newChildren,
            'children_growth_rate' => $childrenGrowthRate,
            'completed_vaccinations' => (int)$completedVaccinations,
            'total_due_vaccinations' => (int)$totalDueVaccinations,
            'completion_rate' => $completionRate,
            'vaccinations_growth_rate' => $vaccinationsGrowthRate,
            'vaccine_types' => (int)$vaccineTypes,
            'medicine_types' => (int)$medicineTypes,
            'contraceptive_types' => (int)$contraceptiveTypes,
            'current_month' => $currentMonth,
            'last_month' => $lastMonth
        ]
    ]);
}

/**
 * جلب إحصائيات الأطفال المفصلة
 */
function getChildrenStats($pdo, $user_id) {
    // توزيع الأطفال حسب العمر
    $ageDistributionQuery = "SELECT 
        CASE 
            WHEN TIMESTAMPDIFF(MONTH, birth_date, CURDATE()) < 6 THEN '0-6 أشهر'
            WHEN TIMESTAMPDIFF(MONTH, birth_date, CURDATE()) < 12 THEN '6-12 شهر'
            WHEN TIMESTAMPDIFF(MONTH, birth_date, CURDATE()) < 24 THEN '1-2 سنة'
            WHEN TIMESTAMPDIFF(MONTH, birth_date, CURDATE()) < 60 THEN '2-5 سنوات'
            ELSE '5+ سنوات'
        END as age_group,
        COUNT(*) as count
        FROM children 
        WHERE user_id = ? 
        GROUP BY age_group
        ORDER BY 
            CASE 
                WHEN age_group = '0-6 أشهر' THEN 1
                WHEN age_group = '6-12 شهر' THEN 2
                WHEN age_group = '1-2 سنة' THEN 3
                WHEN age_group = '2-5 سنوات' THEN 4
                ELSE 5
            END";
    
    $ageStmt = $pdo->prepare($ageDistributionQuery);
    $ageStmt->execute([$user_id]);
    $ageDistribution = $ageStmt->fetchAll();
    
    // الأطفال المضافين في آخر 6 أشهر
    $monthlyAdditionsQuery = "SELECT 
        DATE_FORMAT(created_at, '%Y-%m') as month,
        COUNT(*) as count
        FROM children 
        WHERE user_id = ? 
        AND created_at >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month";
    
    $monthlyStmt = $pdo->prepare($monthlyAdditionsQuery);
    $monthlyStmt->execute([$user_id]);
    $monthlyAdditions = $monthlyStmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'age_distribution' => $ageDistribution,
        'monthly_additions' => $monthlyAdditions
    ]);
}

/**
 * جلب إحصائيات التلقيحات المفصلة
 */
function getVaccinationStats($pdo, $user_id) {
    // توزيع التلقيحات حسب النوع
    $vaccineDistributionQuery = "SELECT 
        cv.vaccine_name,
        COUNT(*) as total_doses,
        SUM(CASE WHEN cv.status = 'completed' THEN 1 ELSE 0 END) as completed_doses
        FROM children_vaccinations cv
        JOIN children c ON cv.child_id = c.id
        WHERE c.user_id = ?
        GROUP BY cv.vaccine_name
        ORDER BY total_doses DESC";
    
    $vaccineStmt = $pdo->prepare($vaccineDistributionQuery);
    $vaccineStmt->execute([$user_id]);
    $vaccineDistribution = $vaccineStmt->fetchAll();
    
    // التلقيحات الشهرية لآخر 6 أشهر
    $monthlyVaccinationsQuery = "SELECT 
        DATE_FORMAT(cv.vaccination_date, '%Y-%m') as month,
        COUNT(*) as count
        FROM children_vaccinations cv
        JOIN children c ON cv.child_id = c.id
        WHERE c.user_id = ? 
        AND cv.status = 'completed'
        AND cv.vaccination_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(cv.vaccination_date, '%Y-%m')
        ORDER BY month";
    
    $monthlyVaccStmt = $pdo->prepare($monthlyVaccinationsQuery);
    $monthlyVaccStmt->execute([$user_id]);
    $monthlyVaccinations = $monthlyVaccStmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'vaccine_distribution' => $vaccineDistribution,
        'monthly_vaccinations' => $monthlyVaccinations
    ]);
}

/**
 * جلب إحصائيات المخزون
 */
function getInventoryStats($pdo, $user_id) {
    // مخزون اللقاحات
    $vaccineStockQuery = "SELECT vaccine_name, quantity, expiry_date 
                         FROM vaccine_stock 
                         WHERE user_id = ? 
                         ORDER BY quantity ASC";
    $vaccineStockStmt = $pdo->prepare($vaccineStockQuery);
    $vaccineStockStmt->execute([$user_id]);
    $vaccineStock = $vaccineStockStmt->fetchAll();
    
    // مخزون الأدوية
    $medicineStockQuery = "SELECT medicine_name, quantity, expiry_date 
                          FROM medicine_stock 
                          WHERE user_id = ? 
                          ORDER BY quantity ASC";
    $medicineStockStmt = $pdo->prepare($medicineStockQuery);
    $medicineStockStmt->execute([$user_id]);
    $medicineStock = $medicineStockStmt->fetchAll();
    
    // مخزون تنظيم الأسرة
    $contraceptiveStockQuery = "SELECT contraceptive_name, quantity, expiry_date 
                               FROM contraceptive_stock 
                               WHERE user_id = ? 
                               ORDER BY quantity ASC";
    $contraceptiveStockStmt = $pdo->prepare($contraceptiveStockQuery);
    $contraceptiveStockStmt->execute([$user_id]);
    $contraceptiveStock = $contraceptiveStockStmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'vaccine_stock' => $vaccineStock,
        'medicine_stock' => $medicineStock,
        'contraceptive_stock' => $contraceptiveStock
    ]);
}

/**
 * جلب الاتجاهات الشهرية
 */
function getMonthlyTrends($pdo, $user_id, $data) {
    $months = $data['months'] ?? 6;
    
    $trendsQuery = "SELECT 
        DATE_FORMAT(created_at, '%Y-%m') as month,
        COUNT(*) as children_added,
        (SELECT COUNT(*) FROM children_vaccinations cv2 
         JOIN children c2 ON cv2.child_id = c2.id 
         WHERE c2.user_id = ? 
         AND cv2.status = 'completed' 
         AND DATE_FORMAT(cv2.vaccination_date, '%Y-%m') = DATE_FORMAT(c.created_at, '%Y-%m')) as vaccinations_completed
        FROM children c
        WHERE c.user_id = ? 
        AND c.created_at >= DATE_SUB(CURDATE(), INTERVAL ? MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month";
    
    $trendsStmt = $pdo->prepare($trendsQuery);
    $trendsStmt->execute([$user_id, $user_id, $months]);
    $trends = $trendsStmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'trends' => $trends
    ]);
}

/**
 * جلب توزيع الأعمار للرسم البياني
 */
function getAgeDistribution($pdo, $user_id) {
    $ageQuery = "SELECT 
        TIMESTAMPDIFF(MONTH, birth_date, CURDATE()) as age_months,
        COUNT(*) as count
        FROM children 
        WHERE user_id = ? 
        GROUP BY age_months
        ORDER BY age_months";
    
    $ageStmt = $pdo->prepare($ageQuery);
    $ageStmt->execute([$user_id]);
    $ageData = $ageStmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'age_data' => $ageData
    ]);
}
?>
