<?php
/**
 * API إدارة الإشعارات مع قاعدة البيانات MySQL
 * Notifications Management API with MySQL Database
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// إعدادات قاعدة البيانات
$host = '127.0.0.1';
$dbname = 'csdb';
$username = 'csdbuser';
$password = 'Aa123456789@';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDO\Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage()
    ]);
    exit();
}

// قراءة البيانات المرسلة
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'بيانات غير صحيحة'
    ]);
    exit();
}

$action = $data['action'] ?? '';
$user_id = $data['user_id'] ?? '';

// التحقق من وجود معرف المستخدم
if (empty($user_id) && $action !== 'get_all_notifications') {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'معرف المستخدم مطلوب'
    ]);
    exit();
}

try {
    switch ($action) {
        case 'create_notification':
            createNotification($pdo, $data);
            break;
            
        case 'get_notifications':
            getNotifications($pdo, $user_id, $data);
            break;
            
        case 'mark_as_read':
            markAsRead($pdo, $data);
            break;
            
        case 'mark_all_as_read':
            markAllAsRead($pdo, $user_id);
            break;
            
        case 'delete_notification':
            deleteNotification($pdo, $data);
            break;
            
        case 'clear_all_notifications':
            clearAllNotifications($pdo, $user_id);
            break;
            
        case 'get_unread_count':
            getUnreadCount($pdo, $user_id);
            break;
            
        case 'create_stock_alert':
            createStockAlert($pdo, $data);
            break;
            
        case 'create_vaccination_reminder':
            createVaccinationReminder($pdo, $data);
            break;
            
        case 'get_all_notifications':
            getAllNotifications($pdo, $data);
            break;
            
        default:
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'إجراء غير صحيح'
            ]);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم: ' . $e->getMessage()
    ]);
}

/**
 * إنشاء إشعار جديد
 */
function createNotification($pdo, $data) {
    $user_id = $data['user_id'];
    $title = $data['title'] ?? '';
    $message = $data['message'] ?? '';
    $type = $data['type'] ?? 'info';
    $priority = $data['priority'] ?? 'normal';
    $metadata = json_encode($data['metadata'] ?? []);
    
    if (empty($title) || empty($message)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'العنوان والرسالة مطلوبان'
        ]);
        return;
    }
    
    $sql = "INSERT INTO notifications (user_id, title, message, type, priority, metadata) 
            VALUES (?, ?, ?, ?, ?, ?)";
    
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute([$user_id, $title, $message, $type, $priority, $metadata]);
    
    if ($result) {
        $notification_id = $pdo->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إنشاء الإشعار بنجاح',
            'notification_id' => $notification_id,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } else {
        throw new Exception('فشل في إنشاء الإشعار');
    }
}

/**
 * جلب الإشعارات للمستخدم
 */
function getNotifications($pdo, $user_id, $data) {
    $limit = $data['limit'] ?? 50;
    $offset = $data['offset'] ?? 0;
    $unread_only = $data['unread_only'] ?? false;
    
    $sql = "SELECT * FROM notifications WHERE user_id = ?";
    $params = [$user_id];
    
    if ($unread_only) {
        $sql .= " AND is_read = 0";
    }
    
    $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $notifications = $stmt->fetchAll();
    
    // تحويل metadata من JSON
    foreach ($notifications as &$notification) {
        $notification['metadata'] = json_decode($notification['metadata'] ?? '{}', true);
        $notification['time_ago'] = getTimeAgo($notification['created_at']);
    }
    
    echo json_encode([
        'success' => true,
        'notifications' => $notifications,
        'count' => count($notifications)
    ]);
}

/**
 * تحديد إشعار كمقروء
 */
function markAsRead($pdo, $data) {
    $notification_id = $data['notification_id'] ?? 0;
    
    if (!$notification_id) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'معرف الإشعار مطلوب'
        ]);
        return;
    }
    
    $sql = "UPDATE notifications SET is_read = 1, read_at = CURRENT_TIMESTAMP WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute([$notification_id]);
    
    echo json_encode([
        'success' => $result,
        'message' => $result ? 'تم تحديد الإشعار كمقروء' : 'فشل في تحديث الإشعار'
    ]);
}

/**
 * تحديد جميع الإشعارات كمقروءة
 */
function markAllAsRead($pdo, $user_id) {
    $sql = "UPDATE notifications SET is_read = 1, read_at = CURRENT_TIMESTAMP 
            WHERE user_id = ? AND is_read = 0";
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute([$user_id]);
    
    $affected_rows = $stmt->rowCount();
    
    echo json_encode([
        'success' => $result,
        'message' => "تم تحديد {$affected_rows} إشعار كمقروء",
        'affected_count' => $affected_rows
    ]);
}

/**
 * حذف إشعار
 */
function deleteNotification($pdo, $data) {
    $notification_id = $data['notification_id'] ?? 0;
    
    if (!$notification_id) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'معرف الإشعار مطلوب'
        ]);
        return;
    }
    
    $sql = "DELETE FROM notifications WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute([$notification_id]);
    
    echo json_encode([
        'success' => $result,
        'message' => $result ? 'تم حذف الإشعار' : 'فشل في حذف الإشعار'
    ]);
}

/**
 * مسح جميع الإشعارات للمستخدم
 */
function clearAllNotifications($pdo, $user_id) {
    $sql = "DELETE FROM notifications WHERE user_id = ?";
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute([$user_id]);
    
    $affected_rows = $stmt->rowCount();
    
    echo json_encode([
        'success' => $result,
        'message' => "تم حذف {$affected_rows} إشعار",
        'deleted_count' => $affected_rows
    ]);
}

/**
 * جلب عدد الإشعارات غير المقروءة
 */
function getUnreadCount($pdo, $user_id) {
    $sql = "SELECT COUNT(*) as unread_count FROM notifications WHERE user_id = ? AND is_read = 0";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$user_id]);
    $result = $stmt->fetch();
    
    echo json_encode([
        'success' => true,
        'unread_count' => (int)$result['unread_count']
    ]);
}

/**
 * إنشاء تنبيه مخزون
 */
function createStockAlert($pdo, $data) {
    $user_id = $data['user_id'];
    $item_name = $data['item_name'] ?? '';
    $item_type = $data['item_type'] ?? 'vaccine'; // vaccine, medicine, contraceptive
    $current_quantity = $data['current_quantity'] ?? 0;
    
    $title = "تحذير: مخزون منخفض ⚠️";
    $message = "مخزون {$item_name} منخفض ({$current_quantity} وحدة متبقية)";
    
    $metadata = [
        'type' => 'stock_alert',
        'item_name' => $item_name,
        'item_type' => $item_type,
        'current_quantity' => $current_quantity,
        'alert_level' => $current_quantity <= 5 ? 'critical' : 'warning'
    ];
    
    $sql = "INSERT INTO notifications (user_id, title, message, type, priority, metadata) 
            VALUES (?, ?, ?, 'warning', 'high', ?)";
    
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute([$user_id, $title, $message, json_encode($metadata)]);
    
    echo json_encode([
        'success' => $result,
        'message' => $result ? 'تم إنشاء تنبيه المخزون' : 'فشل في إنشاء التنبيه',
        'notification_id' => $result ? $pdo->lastInsertId() : null
    ]);
}

/**
 * إنشاء تذكير تلقيح
 */
function createVaccinationReminder($pdo, $data) {
    $user_id = $data['user_id'];
    $child_name = $data['child_name'] ?? '';
    $vaccine_name = $data['vaccine_name'] ?? '';
    $due_date = $data['due_date'] ?? '';
    
    $title = "تذكير: تلقيح مستحق 💉";
    $message = "الطفل {$child_name} يحتاج لقاح {$vaccine_name}";
    if ($due_date) {
        $message .= " في تاريخ {$due_date}";
    }
    
    $metadata = [
        'type' => 'vaccination_reminder',
        'child_name' => $child_name,
        'vaccine_name' => $vaccine_name,
        'due_date' => $due_date
    ];
    
    $sql = "INSERT INTO notifications (user_id, title, message, type, priority, metadata) 
            VALUES (?, ?, ?, 'info', 'normal', ?)";
    
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute([$user_id, $title, $message, json_encode($metadata)]);
    
    echo json_encode([
        'success' => $result,
        'message' => $result ? 'تم إنشاء تذكير التلقيح' : 'فشل في إنشاء التذكير',
        'notification_id' => $result ? $pdo->lastInsertId() : null
    ]);
}

/**
 * جلب جميع الإشعارات (للإدارة)
 */
function getAllNotifications($pdo, $data) {
    $limit = $data['limit'] ?? 100;
    $offset = $data['offset'] ?? 0;
    
    $sql = "SELECT n.*, u.name as user_name, u.center 
            FROM notifications n 
            LEFT JOIN users u ON n.user_id = u.id 
            ORDER BY n.created_at DESC 
            LIMIT ? OFFSET ?";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$limit, $offset]);
    $notifications = $stmt->fetchAll();
    
    foreach ($notifications as &$notification) {
        $notification['metadata'] = json_decode($notification['metadata'] ?? '{}', true);
        $notification['time_ago'] = getTimeAgo($notification['created_at']);
    }
    
    echo json_encode([
        'success' => true,
        'notifications' => $notifications,
        'count' => count($notifications)
    ]);
}

/**
 * حساب الوقت المنقضي
 */
function getTimeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'الآن';
    if ($time < 3600) return floor($time/60) . ' دقيقة';
    if ($time < 86400) return floor($time/3600) . ' ساعة';
    if ($time < 2592000) return floor($time/86400) . ' يوم';
    if ($time < 31536000) return floor($time/2592000) . ' شهر';
    return floor($time/31536000) . ' سنة';
}
?>
