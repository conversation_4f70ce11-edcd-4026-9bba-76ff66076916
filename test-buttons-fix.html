<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 اختبار إصلاح الأزرار</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-section { 
            border: 2px solid #dc3545; 
            margin: 20px 0; 
            padding: 20px; 
            background: white;
            border-radius: 8px;
        }
        .success-section { border-color: #28a745; }
        .warning-section { border-color: #ffc107; }
        .emergency-alert {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .emergency-alert h2 {
            margin: 0 0 15px 0;
            font-size: 24px;
        }
        .emergency-alert p {
            margin: 10px 0;
            font-size: 16px;
        }
        .fix-item {
            background: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .fix-item h4 {
            color: #155724;
            margin: 0 0 10px 0;
        }
        .fix-item p {
            color: #155724;
            margin: 5px 0;
        }
        .test-button {
            display: inline-block;
            padding: 15px 25px;
            margin: 10px;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
            transform: translateY(-2px);
        }
        .test-button.main { background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); }
        .test-button.vaccine { background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%); }
        .test-button.medicine { background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%); }
        .test-button.family { background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%); }
        .test-button.children { background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); }
        .test-button.tasks { background: linear-gradient(135deg, #795548 0%, #5d4037 100%); }
        .test-button.messages { background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%); }
        .test-button.account { background: linear-gradient(135deg, #607d8b 0%, #455a64 100%); }
        .status { 
            padding: 15px; margin: 10px 0; border-radius: 5px; font-weight: bold;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        .test-results {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .test-log {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.6;
            white-space: pre-wrap;
        }
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .function-status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .function-status.exists { background: #28a745; }
        .function-status.missing { background: #dc3545; }
        .function-status.error { background: #ffc107; }
    </style>
</head>
<body>
    <div class="emergency-alert">
        <h2>🚨 إصلاح عاجل لمشاكل الأزرار</h2>
        <p><strong>المشكلة:</strong> الأزرار لا تعمل في الموقع</p>
        <p><strong>السبب:</strong> دوال JavaScript معطلة أو مفقودة</p>
        <p><strong>الحالة:</strong> تم تطبيق إصلاحات عاجلة</p>
    </div>
    
    <div class="test-section success-section">
        <h2>✅ الإصلاحات المطبقة</h2>
        
        <div class="fix-item">
            <h4>🔧 إصلاح 1: تبسيط دوال التنقل</h4>
            <p><strong>المشكلة:</strong> دوال async معقدة تسبب أخطاء</p>
            <p><strong>الحل:</strong> تبسيط الدوال وإزالة التعقيدات غير الضرورية</p>
            <p><strong>النتيجة:</strong> أزرار تعمل بسرعة وموثوقية</p>
        </div>
        
        <div class="fix-item">
            <h4>⚡ إصلاح 2: إزالة showLoader/hideLoader</h4>
            <p><strong>المشكلة:</strong> مؤشرات التحميل تسبب تأخيرات وأخطاء</p>
            <p><strong>الحل:</strong> تنقل مباشر بدون مؤشرات تحميل غير ضرورية</p>
            <p><strong>النتيجة:</strong> استجابة فورية للأزرار</p>
        </div>
        
        <div class="fix-item">
            <h4>🛡️ إصلاح 3: معالجة أخطاء محسنة</h4>
            <p><strong>المشكلة:</strong> الأخطاء تمنع عمل الأزرار</p>
            <p><strong>الحل:</strong> آليات احتياطية لضمان عمل الأزرار حتى لو فشلت العمليات الأخرى</p>
            <p><strong>النتيجة:</strong> أزرار تعمل دائماً</p>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🧪 اختبار جميع الأزرار</h2>
        <p>اضغط على الأزرار أدناه لاختبار كل دالة تنقل:</p>
        
        <div class="button-grid">
            <button class="test-button main" onclick="testFunction('showMainPage')">
                🏠 الصفحة الرئيسية
                <span class="function-status" id="status-showMainPage"></span>
            </button>
            
            <button class="test-button" onclick="testFunction('showVaccinationCalculator')">
                📅 حاسبة التلقيح
                <span class="function-status" id="status-showVaccinationCalculator"></span>
            </button>
            
            <button class="test-button vaccine" onclick="testFunction('showVaccineManagement')">
                💉 إدارة اللقاحات
                <span class="function-status" id="status-showVaccineManagement"></span>
            </button>
            
            <button class="test-button medicine" onclick="testFunction('showMedicineManagement')">
                💊 إدارة الأدوية
                <span class="function-status" id="status-showMedicineManagement"></span>
            </button>
            
            <button class="test-button family" onclick="testFunction('showFamilyPlanningManagement')">
                👨‍👩‍👧‍👦 تنظيم الأسرة
                <span class="function-status" id="status-showFamilyPlanningManagement"></span>
            </button>
            
            <button class="test-button children" onclick="testFunction('showChildrenRegistry')">
                👶 سجل الأطفال
                <span class="function-status" id="status-showChildrenRegistry"></span>
            </button>
            
            <button class="test-button tasks" onclick="testFunction('showTodoApp')">
                📋 قائمة المهام
                <span class="function-status" id="status-showTodoApp"></span>
            </button>
            
            <button class="test-button messages" onclick="testFunction('showMessagesPage')">
                💬 الرسائل
                <span class="function-status" id="status-showMessagesPage"></span>
            </button>
            
            <button class="test-button account" onclick="testFunction('showAccountManagement')">
                👤 إدارة الحساب
                <span class="function-status" id="status-showAccountManagement"></span>
            </button>
        </div>
        
        <div style="margin: 20px 0;">
            <button class="test-button" onclick="testAllFunctions()">🧪 اختبار جميع الدوال</button>
            <button class="test-button" onclick="checkFunctionExistence()">🔍 فحص وجود الدوال</button>
            <button class="test-button" onclick="clearResults()">🗑️ مسح النتائج</button>
        </div>
    </div>
    
    <div id="status" class="status info">
        <strong>الحالة:</strong> جاهز لاختبار إصلاحات الأزرار
    </div>
    
    <div class="test-section">
        <h2>📊 نتائج الاختبار</h2>
        <div class="test-results">
            <div class="test-log" id="testLog">لا توجد نتائج بعد...</div>
        </div>
    </div>
    
    <div class="test-section warning-section">
        <h2>⚠️ تعليمات الاختبار</h2>
        <ol>
            <li><strong>اختبر كل زر:</strong> تأكد من أن جميع الأزرار تستجيب</li>
            <li><strong>راقب Console:</strong> افتح Developer Tools وراقب الأخطاء</li>
            <li><strong>اختبر التنقل:</strong> تأكد من أن الصفحات تتغير بشكل صحيح</li>
            <li><strong>اختبر الشريط الجانبي:</strong> تأكد من تحديث حالة النشاط</li>
        </ol>
    </div>

    <script>
        let testResults = [];
        let functionTests = {};
        
        // قائمة الدوال المطلوبة
        const requiredFunctions = [
            'showMainPage',
            'showVaccinationCalculator', 
            'showVaccineManagement',
            'showMedicineManagement',
            'showFamilyPlanningManagement',
            'showChildrenRegistry',
            'showTodoApp',
            'showMessagesPage',
            'showAccountManagement'
        ];
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<strong>الحالة:</strong> ${message}`;
            status.className = `status ${type}`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function addTestResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            testResults.push(logEntry);
            
            const testLog = document.getElementById('testLog');
            testLog.textContent = testResults.join('\n');
            testLog.scrollTop = testLog.scrollHeight;
            
            console.log(logEntry);
        }
        
        function updateFunctionStatus(functionName, status) {
            const statusElement = document.getElementById(`status-${functionName}`);
            if (statusElement) {
                statusElement.className = `function-status ${status}`;
            }
        }
        
        function testFunction(functionName) {
            addTestResult(`🧪 اختبار دالة: ${functionName}`);
            updateStatus(`جاري اختبار ${functionName}...`, 'info');
            
            try {
                // فحص وجود الدالة
                if (typeof window[functionName] === 'function') {
                    addTestResult(`✅ الدالة ${functionName} موجودة`, 'success');
                    updateFunctionStatus(functionName, 'exists');
                    
                    // محاولة تشغيل الدالة
                    try {
                        window[functionName]();
                        addTestResult(`✅ تم تشغيل ${functionName} بنجاح`, 'success');
                        functionTests[functionName] = 'success';
                        updateStatus(`✅ ${functionName} تعمل بشكل صحيح`, 'pass');
                    } catch (execError) {
                        addTestResult(`❌ خطأ في تشغيل ${functionName}: ${execError.message}`, 'error');
                        functionTests[functionName] = 'error';
                        updateFunctionStatus(functionName, 'error');
                        updateStatus(`❌ خطأ في تشغيل ${functionName}`, 'fail');
                    }
                } else {
                    addTestResult(`❌ الدالة ${functionName} غير موجودة`, 'error');
                    functionTests[functionName] = 'missing';
                    updateFunctionStatus(functionName, 'missing');
                    updateStatus(`❌ الدالة ${functionName} مفقودة`, 'fail');
                }
            } catch (error) {
                addTestResult(`❌ خطأ في اختبار ${functionName}: ${error.message}`, 'error');
                functionTests[functionName] = 'error';
                updateFunctionStatus(functionName, 'error');
                updateStatus(`❌ خطأ في اختبار ${functionName}`, 'fail');
            }
        }
        
        function checkFunctionExistence() {
            addTestResult('🔍 فحص وجود جميع الدوال المطلوبة');
            updateStatus('جاري فحص وجود الدوال...', 'info');
            
            let existingFunctions = 0;
            let missingFunctions = 0;
            
            requiredFunctions.forEach(functionName => {
                if (typeof window[functionName] === 'function') {
                    addTestResult(`✅ ${functionName} موجودة`, 'success');
                    updateFunctionStatus(functionName, 'exists');
                    existingFunctions++;
                } else {
                    addTestResult(`❌ ${functionName} مفقودة`, 'error');
                    updateFunctionStatus(functionName, 'missing');
                    missingFunctions++;
                }
            });
            
            addTestResult(`📊 النتيجة: ${existingFunctions} موجودة، ${missingFunctions} مفقودة`);
            
            if (missingFunctions === 0) {
                updateStatus('✅ جميع الدوال موجودة', 'pass');
            } else {
                updateStatus(`⚠️ ${missingFunctions} دالة مفقودة`, 'warning');
            }
        }
        
        async function testAllFunctions() {
            addTestResult('🧪 بدء اختبار شامل لجميع الدوال');
            updateStatus('جاري اختبار جميع الدوال...', 'info');
            
            for (const functionName of requiredFunctions) {
                testFunction(functionName);
                // تأخير قصير بين الاختبارات
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            // تلخيص النتائج
            const successCount = Object.values(functionTests).filter(status => status === 'success').length;
            const errorCount = Object.values(functionTests).filter(status => status === 'error').length;
            const missingCount = Object.values(functionTests).filter(status => status === 'missing').length;
            
            addTestResult('=== ملخص الاختبار الشامل ===');
            addTestResult(`✅ دوال تعمل: ${successCount}`);
            addTestResult(`❌ دوال بها أخطاء: ${errorCount}`);
            addTestResult(`❌ دوال مفقودة: ${missingCount}`);
            
            if (errorCount === 0 && missingCount === 0) {
                updateStatus('🎉 جميع الدوال تعمل بشكل ممتاز!', 'pass');
            } else {
                updateStatus(`⚠️ ${errorCount + missingCount} دالة تحتاج إصلاح`, 'warning');
            }
        }
        
        function clearResults() {
            testResults = [];
            functionTests = {};
            document.getElementById('testLog').textContent = 'تم مسح النتائج';
            
            // إعادة تعيين مؤشرات الحالة
            requiredFunctions.forEach(functionName => {
                updateFunctionStatus(functionName, '');
            });
            
            updateStatus('تم مسح جميع النتائج', 'info');
        }
        
        // تهيئة تلقائية
        window.onload = function() {
            updateStatus('جاهز لاختبار إصلاحات الأزرار', 'info');
            addTestResult('تم تحميل صفحة اختبار إصلاحات الأزرار');
            addTestResult('جميع الإصلاحات مطبقة ومتاحة للاختبار');
            
            // فحص تلقائي لوجود الدوال
            setTimeout(() => {
                checkFunctionExistence();
            }, 1000);
        };
    </script>
</body>
</html>
